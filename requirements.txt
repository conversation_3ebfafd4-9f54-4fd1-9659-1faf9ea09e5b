# JaVi Agent Translator - Requirements
# Core dependencies for the application

# Modern GUI framework
dearpygui==2.0.0

# Excel processing and automation
xlwings>=0.33.0
openpyxl>=3.1.5
pandas>=2.2.0

# AI/Translation services
openai>=1.84.0
google-generativeai>=0.8.5

# Environment and configuration
python-dotenv>=1.1.0

# macOS specific (for xlwings Excel automation)
appscript>=1.3.0; sys_platform == "darwin"

# Image processing (for creating test files and potential future features)
pillow>=11.0.0

# For building standalone executable
pyinstaller>=6.14.0

# Note: Sub-dependencies (numpy, lxml, psutil, httpx, etc.)
# will be automatically installed by the above packages