#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON><PERSON><PERSON> to create app icon from Haposoft logo
"""

import os
import sys
from PIL import Image, ImageDraw, ImageFont
import requests
from io import Bytes<PERSON>

def create_haposoft_icon():
    """Create Haposoft icon for the app"""
    print("🎨 Creating Haposoft icon...")
    
    # Create a 512x512 icon (standard macOS app icon size)
    size = 512
    icon = Image.new('RGBA', (size, size), (255, 255, 255, 0))
    draw = ImageDraw.Draw(icon)
    
    # Background circle (light green)
    margin = 20
    circle_size = size - 2 * margin
    draw.ellipse([margin, margin, margin + circle_size, margin + circle_size], 
                fill=(139, 195, 74, 255), outline=None)
    
    # Inner white circle
    inner_margin = 40
    inner_size = size - 2 * inner_margin
    draw.ellipse([inner_margin, inner_margin, inner_margin + inner_size, inner_margin + inner_size], 
                fill=(255, 255, 255, 255), outline=None)
    
    # Owl body (green)
    owl_center_x = size // 2
    owl_center_y = size // 2 - 30
    owl_width = 120
    owl_height = 100
    
    # Owl body shape (simplified)
    owl_left = owl_center_x - owl_width // 2
    owl_right = owl_center_x + owl_width // 2
    owl_top = owl_center_y - owl_height // 2
    owl_bottom = owl_center_y + owl_height // 2
    
    draw.ellipse([owl_left, owl_top, owl_right, owl_bottom], 
                fill=(139, 195, 74, 255), outline=None)
    
    # Owl ears
    ear_size = 25
    # Left ear
    draw.polygon([(owl_left + 20, owl_top), 
                  (owl_left + 20 - ear_size, owl_top - ear_size),
                  (owl_left + 20 + ear_size, owl_top - ear_size)], 
                fill=(139, 195, 74, 255))
    # Right ear  
    draw.polygon([(owl_right - 20, owl_top),
                  (owl_right - 20 - ear_size, owl_top - ear_size), 
                  (owl_right - 20 + ear_size, owl_top - ear_size)], 
                fill=(139, 195, 74, 255))
    
    # Owl eyes (white circles with black pupils)
    eye_size = 35
    eye_y = owl_center_y - 15
    
    # Left eye
    left_eye_x = owl_center_x - 25
    draw.ellipse([left_eye_x - eye_size//2, eye_y - eye_size//2, 
                  left_eye_x + eye_size//2, eye_y + eye_size//2], 
                fill=(255, 255, 255, 255), outline=(0, 0, 0, 255), width=2)
    
    # Left pupil
    pupil_size = 15
    draw.ellipse([left_eye_x - pupil_size//2, eye_y - pupil_size//2,
                  left_eye_x + pupil_size//2, eye_y + pupil_size//2], 
                fill=(0, 0, 0, 255))
    
    # Right eye
    right_eye_x = owl_center_x + 25
    draw.ellipse([right_eye_x - eye_size//2, eye_y - eye_size//2,
                  right_eye_x + eye_size//2, eye_y + eye_size//2], 
                fill=(255, 255, 255, 255), outline=(0, 0, 0, 255), width=2)
    
    # Right pupil
    draw.ellipse([right_eye_x - pupil_size//2, eye_y - pupil_size//2,
                  right_eye_x + pupil_size//2, eye_y + pupil_size//2], 
                fill=(0, 0, 0, 255))
    
    # Owl beak (small triangle)
    beak_y = owl_center_y + 10
    draw.polygon([(owl_center_x, beak_y), 
                  (owl_center_x - 8, beak_y - 12),
                  (owl_center_x + 8, beak_y - 12)], 
                fill=(255, 165, 0, 255))
    
    # Owl feet/claws (simple lines)
    feet_y = owl_bottom + 10
    draw.line([(owl_center_x - 20, feet_y), (owl_center_x - 20, feet_y + 15)], 
              fill=(101, 101, 101, 255), width=3)
    draw.line([(owl_center_x + 20, feet_y), (owl_center_x + 20, feet_y + 15)], 
              fill=(101, 101, 101, 255), width=3)
    
    # Add "haposoft" text
    try:
        # Try to use a nice font, fallback to default if not available
        font_size = 36
        try:
            font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", font_size)
        except:
            font = ImageFont.load_default()
        
        text = "haposoft"
        text_bbox = draw.textbbox((0, 0), text, font=font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]
        
        text_x = (size - text_width) // 2
        text_y = size - 80
        
        draw.text((text_x, text_y), text, fill=(101, 101, 101, 255), font=font)
        
    except Exception as e:
        print(f"⚠️ Could not add text: {e}")
    
    return icon

def save_icon_formats(icon):
    """Save icon in different formats for macOS"""
    print("💾 Saving icon files...")
    
    # Save as PNG for general use
    icon.save("haposoft_icon.png", "PNG")
    print("✅ Saved: haposoft_icon.png")
    
    # Save as ICO for Windows compatibility
    icon.save("haposoft_icon.ico", "ICO")
    print("✅ Saved: haposoft_icon.ico")
    
    # Create ICNS for macOS (requires pillow with ICNS support)
    try:
        # Create different sizes for ICNS
        sizes = [16, 32, 64, 128, 256, 512]
        icon_images = []
        
        for size in sizes:
            resized = icon.resize((size, size), Image.Resampling.LANCZOS)
            icon_images.append(resized)
        
        # Save the largest one as ICNS
        icon.save("haposoft_icon.icns", "ICNS")
        print("✅ Saved: haposoft_icon.icns")
        
    except Exception as e:
        print(f"⚠️ Could not create ICNS: {e}")
        print("💡 ICNS creation requires pillow with ICNS support")

def main():
    """Main function"""
    print("🎨 Creating Haposoft app icon...")
    
    try:
        # Create the icon
        icon = create_haposoft_icon()
        
        # Save in different formats
        save_icon_formats(icon)
        
        print("\n✅ Icon creation completed!")
        print("📁 Files created:")
        print("   - haposoft_icon.png (general use)")
        print("   - haposoft_icon.ico (Windows)")
        print("   - haposoft_icon.icns (macOS)")
        
    except Exception as e:
        print(f"❌ Error creating icon: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
