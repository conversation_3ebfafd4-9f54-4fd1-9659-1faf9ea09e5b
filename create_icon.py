#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON><PERSON><PERSON> to create app icon from Haposoft original logo
"""

import os
import sys
from PIL import Image, ImageDraw, ImageFont

def create_haposoft_icon_from_original():
    """Create Haposoft icon from original logo file"""
    print("🎨 Creating Haposoft icon from original logo...")

    # Check if original logo exists
    original_logo_path = "original_logo.webp"
    if not os.path.exists(original_logo_path):
        print(f"❌ Original logo not found: {original_logo_path}")
        return None

    try:
        # Load the original logo
        original_logo = Image.open(original_logo_path)
        print(f"✅ Loaded original logo: {original_logo.size}")

        # Convert to RGBA if needed
        if original_logo.mode != 'RGBA':
            original_logo = original_logo.convert('RGBA')

        # Create icon sizes for different uses
        icon_size = 512  # Standard macOS app icon size

        # Resize the original logo to fit icon size while maintaining aspect ratio
        original_logo.thumbnail((icon_size, icon_size), Image.Resampling.LANCZOS)

        # Create a new icon with transparent background
        icon = Image.new('RGBA', (icon_size, icon_size), (255, 255, 255, 0))

        # Calculate position to center the logo
        logo_width, logo_height = original_logo.size
        x = (icon_size - logo_width) // 2
        y = (icon_size - logo_height) // 2

        # Paste the original logo onto the icon
        icon.paste(original_logo, (x, y), original_logo)

        print(f"✅ Created icon with size: {icon.size}")
        return icon

    except Exception as e:
        print(f"❌ Error processing original logo: {e}")
        return None

def create_haposoft_icon():
    """Create Haposoft icon - try original first, fallback to manual creation"""

    # Try to use original logo first
    icon = create_haposoft_icon_from_original()
    if icon:
        return icon

    print("⚠️ Could not use original logo, creating manual version...")

    # Fallback to manual creation (simplified version)
    size = 512
    icon = Image.new('RGBA', (size, size), (255, 255, 255, 0))
    draw = ImageDraw.Draw(icon)

    # Background circle (light green)
    margin = 20
    circle_size = size - 2 * margin
    draw.ellipse([margin, margin, margin + circle_size, margin + circle_size],
                fill=(139, 195, 74, 255), outline=None)

    # Inner white circle
    inner_margin = 40
    inner_size = size - 2 * inner_margin
    draw.ellipse([inner_margin, inner_margin, inner_margin + inner_size, inner_margin + inner_size],
                fill=(255, 255, 255, 255), outline=None)

    # Simple owl representation
    owl_center_x = size // 2
    owl_center_y = size // 2 - 30

    # Owl body (green circle)
    owl_radius = 60
    draw.ellipse([owl_center_x - owl_radius, owl_center_y - owl_radius,
                  owl_center_x + owl_radius, owl_center_y + owl_radius],
                fill=(139, 195, 74, 255), outline=None)

    # Owl eyes (white circles with black pupils)
    eye_size = 25
    eye_y = owl_center_y - 10

    # Left eye
    left_eye_x = owl_center_x - 20
    draw.ellipse([left_eye_x - eye_size//2, eye_y - eye_size//2,
                  left_eye_x + eye_size//2, eye_y + eye_size//2],
                fill=(255, 255, 255, 255), outline=(0, 0, 0, 255), width=2)

    # Left pupil
    pupil_size = 10
    draw.ellipse([left_eye_x - pupil_size//2, eye_y - pupil_size//2,
                  left_eye_x + pupil_size//2, eye_y + pupil_size//2],
                fill=(0, 0, 0, 255))

    # Right eye
    right_eye_x = owl_center_x + 20
    draw.ellipse([right_eye_x - eye_size//2, eye_y - eye_size//2,
                  right_eye_x + eye_size//2, eye_y + eye_size//2],
                fill=(255, 255, 255, 255), outline=(0, 0, 0, 255), width=2)

    # Right pupil
    draw.ellipse([right_eye_x - pupil_size//2, eye_y - pupil_size//2,
                  right_eye_x + pupil_size//2, eye_y + pupil_size//2],
                fill=(0, 0, 0, 255))

    # Add "haposoft" text
    try:
        font_size = 36
        try:
            font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", font_size)
        except:
            font = ImageFont.load_default()

        text = "haposoft"
        text_bbox = draw.textbbox((0, 0), text, font=font)
        text_width = text_bbox[2] - text_bbox[0]

        text_x = (size - text_width) // 2
        text_y = size - 80

        draw.text((text_x, text_y), text, fill=(101, 101, 101, 255), font=font)

    except Exception as e:
        print(f"⚠️ Could not add text: {e}")

    return icon

def save_icon_formats(icon):
    """Save icon in different formats for macOS"""
    print("💾 Saving icon files...")
    
    # Save as PNG for general use
    icon.save("haposoft_icon.png", "PNG")
    print("✅ Saved: haposoft_icon.png")
    
    # Save as ICO for Windows compatibility
    icon.save("haposoft_icon.ico", "ICO")
    print("✅ Saved: haposoft_icon.ico")
    
    # Create ICNS for macOS (requires pillow with ICNS support)
    try:
        # Create different sizes for ICNS
        sizes = [16, 32, 64, 128, 256, 512]
        icon_images = []
        
        for size in sizes:
            resized = icon.resize((size, size), Image.Resampling.LANCZOS)
            icon_images.append(resized)
        
        # Save the largest one as ICNS
        icon.save("haposoft_icon.icns", "ICNS")
        print("✅ Saved: haposoft_icon.icns")
        
    except Exception as e:
        print(f"⚠️ Could not create ICNS: {e}")
        print("💡 ICNS creation requires pillow with ICNS support")

def main():
    """Main function"""
    print("🎨 Creating Haposoft app icon...")
    
    try:
        # Create the icon
        icon = create_haposoft_icon()
        
        # Save in different formats
        save_icon_formats(icon)
        
        print("\n✅ Icon creation completed!")
        print("📁 Files created:")
        print("   - haposoft_icon.png (general use)")
        print("   - haposoft_icon.ico (Windows)")
        print("   - haposoft_icon.icns (macOS)")
        
    except Exception as e:
        print(f"❌ Error creating icon: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
