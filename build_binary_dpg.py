#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Build script for creating binary executables of Excel Translator GUI (Dear PyGui Version)
Supports both macOS and Windows
"""

import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{'='*50}")
    print(f"🔄 {description}")
    print(f"{'='*50}")
    print(f"Command: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            print("Output:", result.stdout)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"Error code: {e.returncode}")
        if e.stdout:
            print("Output:", e.stdout)
        if e.stderr:
            print("Error:", e.stderr)
        return False

def install_requirements():
    """Install/update requirements from requirements.txt"""
    print("📦 Installing/updating requirements...")

    # Check if requirements.txt exists
    if not os.path.exists('requirements.txt'):
        print("❌ requirements.txt not found!")
        return False

    # Install requirements
    command = "pip install -r requirements.txt --upgrade"
    success = run_command(command, "Installing/updating requirements")

    if success:
        print("✅ Requirements installed/updated successfully")
        return True
    else:
        print("❌ Failed to install requirements")
        return False

def check_dependencies():
    """Check if all required dependencies are installed"""
    print("🔍 Checking dependencies...")

    required_modules = [
        'dearpygui',
        'xlwings',
        'openpyxl',
        'pandas',
        'openai',
        'dotenv',
        'PyInstaller'
    ]

    missing = []
    for module in required_modules:
        try:
            if module == 'dearpygui':
                import dearpygui
            elif module == 'xlwings':
                import xlwings
            elif module == 'openpyxl':
                import openpyxl
            elif module == 'pandas':
                import pandas
            elif module == 'openai':
                import openai
            elif module == 'dotenv':
                import dotenv
            elif module == 'PyInstaller':
                import PyInstaller
        except ImportError:
            missing.append(module)

    if missing:
        print(f"❌ Missing dependencies: {', '.join(missing)}")
        print("Please install them with: pip install -r requirements.txt")
        return False

    print("✅ All dependencies are installed")
    return True

def create_spec_file():
    """Create PyInstaller spec file for Dear PyGui version"""
    spec_content = """# -*- mode: python ; coding: utf-8 -*-
import sys

block_cipher = None

a = Analysis(
    ['excel_translator_dpg.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('trans_excel2.py', '.'),
    ],
    hiddenimports=[
        'dearpygui',
        'dearpygui.dearpygui',
        'xlwings',
        'openpyxl',
        'pandas',
        'openai',
        'dotenv',
        'json',
        'threading',
        'webbrowser',
        'os',
        'sys',
        'pathlib',
        'time'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'scipy',
        'numpy'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='ExcelTranslatorDPG',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

# macOS app bundle
if sys.platform == 'darwin':
    app = BUNDLE(
        exe,
        name='ExcelTranslatorDPG.app',
        icon=None,
        bundle_identifier='com.exceltranslator.dpg.app',
        info_plist={
            'CFBundleDisplayName': 'Excel Translator (DPG)',
            'CFBundleVersion': '2.0.0',
            'CFBundleShortVersionString': '2.0.0',
            'NSHighResolutionCapable': True,
            'LSMinimumSystemVersion': '10.14.0',
        },
    )
"""
    
    with open('excel_translator_dpg.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ Spec file created: excel_translator_dpg.spec")

def build_executable():
    """Build the executable using PyInstaller"""
    system = platform.system().lower()
    
    print(f"🏗️ Building Dear PyGui executable for {system}...")
    
    # Create spec file for better control
    create_spec_file()
    
    # Build command
    command = "pyinstaller --clean excel_translator_dpg.spec"
    
    success = run_command(command, f"Building {system} executable")
    
    if success:
        # Find the built executable
        dist_dir = Path("dist")
        if system == "darwin":
            exe_path = dist_dir / "ExcelTranslatorDPG.app"
            if exe_path.exists():
                print(f"✅ macOS app created: {exe_path}")
                print("You can copy this .app file to Applications folder")
        elif system == "windows":
            exe_path = dist_dir / "ExcelTranslatorDPG.exe"
            if exe_path.exists():
                print(f"✅ Windows executable created: {exe_path}")
        else:
            exe_path = dist_dir / "ExcelTranslatorDPG"
            if exe_path.exists():
                print(f"✅ Linux executable created: {exe_path}")
        
        return True
    
    return False

def cleanup():
    """Clean up temporary files"""
    print("\n🧹 Cleaning up temporary files...")
    
    temp_dirs = ['build', '__pycache__']
    temp_files = ['excel_translator_dpg.spec']
    
    for temp_dir in temp_dirs:
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
            print(f"  Removed: {temp_dir}/")
    
    for temp_file in temp_files:
        if os.path.exists(temp_file):
            os.remove(temp_file)
            print(f"  Removed: {temp_file}")

def main():
    """Main build function"""
    print("🚀 Excel Translator GUI - Dear PyGui Binary Builder")
    print(f"Platform: {platform.system()} {platform.release()}")
    print(f"Python: {sys.version}")
    
    # Check if required files exist
    required_files = ['excel_translator_dpg.py', 'trans_excel2.py']
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ Required file not found: {file}")
            return False

    # Install/update requirements first
    if not install_requirements():
        return False

    # Check dependencies
    if not check_dependencies():
        return False
    
    # Build executable
    if build_executable():
        print("\n🎉 Build completed successfully!")
        print("\n📦 Your executable is in the 'dist' folder")
        
        # Platform-specific instructions
        system = platform.system().lower()
        if system == "darwin":
            print("\n📱 macOS Instructions:")
            print("1. Copy ExcelTranslatorDPG.app to your Applications folder")
            print("2. First run: Right-click → Open (to bypass Gatekeeper)")
            print("3. After first run, you can double-click normally")
            print("4. The Dear PyGui version is more stable on macOS")
        elif system == "windows":
            print("\n💻 Windows Instructions:")
            print("1. Copy ExcelTranslatorDPG.exe to desired location")
            print("2. Create desktop shortcut if needed")
            print("3. Windows Defender might ask for permission on first run")
            print("4. Dear PyGui provides better performance than tkinter")
        
        print("\n✨ Dear PyGui Version Features:")
        print("- Modern, hardware-accelerated GUI")
        print("- Better performance and stability")
        print("- Native file dialogs")
        print("- Responsive interface")
        print("- No macOS flickering issues")
        
        print("\n⚠️ Important:")
        print("- Keep trans_excel2.py in the same folder as the executable")
        print("- Internet connection required for translation")
        print("- First run may be slower due to antivirus scanning")
        
        return True
    else:
        print("\n❌ Build failed!")
        return False

if __name__ == "__main__":
    try:
        success = main()
        
        # Ask about cleanup
        if success:
            response = input("\n🗑️ Clean up temporary files? (y/n): ").strip().lower()
            if response in ['y', 'yes']:
                cleanup()
                print("✅ Cleanup completed")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Build cancelled by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        import traceback
        traceback.print_exc() 