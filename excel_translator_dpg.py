#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JaVi Agent Translator GUI - Dear PyGui Version
A modern interface for translating Excel files using Google's Gemini AI
"""

import os
import sys
import threading
import json
import webbrowser
import time
import uuid
from pathlib import Path

import dearpygui.dearpygui as dpg

# Check if running as executable
if getattr(sys, 'frozen', False):
    SCRIPT_DIR = os.path.dirname(sys.executable)
else:
    SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

class ExcelTranslatorApp:
    def __init__(self):
        self.file_path = ""
        self.output_path = ""
        self.api_key = ""
        self.target_language = "ja"
        self.is_translating = False
        self.translation_thread = None
        
        # Load settings first
        self.load_settings()
        
        # Setup DPG
        self.setup_dpg()
        
    def setup_dpg(self):
        """Initialize Dear PyGui"""
        dpg.create_context()
        
        # Setup fonts - simplified to avoid errors
        try:
            # Create font registry
            with dpg.font_registry():
                # Try to add default font with basic range first
                default_font = dpg.add_font("", 15)
                
                # Add character ranges for Unicode support
                dpg.add_font_range_hint(dpg.mvFontRangeHint_Default)
                dpg.add_font_range_hint(dpg.mvFontRangeHint_Japanese)
                dpg.add_font_range_hint(dpg.mvFontRangeHint_Vietnamese)
                
        except Exception as e:
            print(f"Font setup warning: {e}")
            # Continue without custom font
            default_font = None
        
        # Create main window
        with dpg.window(label="JaVi Agent Translator", tag="main_window", width=600, height=550):
            # Title
            dpg.add_text("🚀 JaVi Agent Translator", color=[255, 255, 255])
            dpg.add_separator()
            dpg.add_spacer(height=10)
            
            # Step 1: File Selection
            with dpg.group(horizontal=False):
                dpg.add_text("Step 1: Select Excel File", color=[100, 200, 255])
                
                with dpg.group(horizontal=True):
                    dpg.add_input_text(
                        tag="file_input",
                        hint="File path will appear here after browsing...",
                        width=380,
                        readonly=False,  # Allow manual input too
                        callback=self.on_file_input_change
                    )
                    dpg.add_button(label="Browse", callback=self.browse_file, width=80)
                    dpg.add_button(label="Clear", callback=self.clear_file_input, width=60)
                
                dpg.add_text("💡 Click 'Browse' to select file, or paste file path directly", 
                           color=[150, 150, 150], wrap=550)
                
                dpg.add_spacer(height=15)
            
            # Step 2: Language Selection
            with dpg.group(horizontal=False):
                dpg.add_text("Step 2: Choose Target Language", color=[100, 200, 255])
                
                dpg.add_radio_button(
                    ["Japanese (ja)", "Vietnamese (vi)"],
                    tag="language_radio",
                    default_value=0,
                    horizontal=True,
                    callback=self.on_language_change
                )
                
                dpg.add_spacer(height=15)
            
            # Step 3: API Key
            with dpg.group(horizontal=False):
                dpg.add_text("Step 3: Enter Gemini API Key", color=[255, 100, 100])
                
                with dpg.group(horizontal=True):
                    dpg.add_input_text(
                        tag="api_input",
                        hint="Enter your Gemini API key here...",
                        width=300,
                        password=True
                    )
                    dpg.add_checkbox(label="Show", tag="show_api", callback=self.toggle_api_visibility)
                    dpg.add_button(label="Get Key", callback=self.open_api_page)
                    dpg.add_button(label="Save", callback=self.save_api_key)
                
                dpg.add_text("• Click 'Get Key' → Sign in → Create API Key → Paste above → Click 'Save'", 
                           color=[150, 150, 255], wrap=550)
                
                dpg.add_spacer(height=15)
            
            # Step 4: Output Folder
            with dpg.group(horizontal=False):
                dpg.add_text("Step 4: Choose Output Folder (Optional)", color=[100, 200, 255])
                
                with dpg.group(horizontal=True):
                    dpg.add_input_text(
                        tag="output_input",
                        hint="Leave empty for auto folder, or browse/type custom path",
                        width=350,
                        readonly=False
                    )
                    dpg.add_button(label="Browse Folder", callback=self.browse_output, width=100)
                    dpg.add_button(label="Auto", callback=self.set_auto_output, width=60)
                
                dpg.add_text("💡 Auto = creates 'output' folder next to your Excel file", 
                           color=[150, 150, 150], wrap=550)
                
                dpg.add_spacer(height=20)
            
            # Status Section
            with dpg.group(horizontal=False):
                dpg.add_text("Status:", color=[100, 200, 255])
                dpg.add_text("Ready to translate", tag="status_text", color=[100, 255, 100])
                
                # Progress bar
                dpg.add_progress_bar(tag="progress_bar", width=550, show=False)
                
                dpg.add_spacer(height=20)
            
            # Control Buttons
            with dpg.group(horizontal=True):
                dpg.add_button(
                    label="START TRANSLATION",
                    tag="start_btn",
                    callback=self.start_translation,
                    width=150,
                    height=40
                )
                dpg.add_button(
                    label="STOP",
                    tag="stop_btn", 
                    callback=self.stop_translation,
                    width=80,
                    height=40,
                    enabled=False
                )
                dpg.add_button(
                    label="Open Output Folder",
                    callback=self.open_output_folder,
                    width=150,
                    height=40
                )
                dpg.add_button(
                    label="Exit",
                    callback=self.close_app,
                    width=80,
                    height=40
                )
        
        # Set initial values
        if self.api_key:
            dpg.set_value("api_input", self.api_key)
        if self.output_path:
            dpg.set_value("output_input", self.output_path)

        # Set language radio button based on saved settings
        language_index = 0 if self.target_language == "ja" else 1
        dpg.set_value("language_radio", language_index)
        
        # Configure DPG
        dpg.create_viewport(title="JaVi Agent Translator", width=650, height=600, resizable=False)
        dpg.setup_dearpygui()
        dpg.set_primary_window("main_window", True)
        
        # Bind font if successfully loaded
        try:
            if default_font:
                dpg.bind_font(default_font)
        except Exception as e:
            print(f"Font binding warning: {e}")
        
        dpg.show_viewport()
        
        # Update status
        if self.api_key:
            dpg.set_value("status_text", "Settings loaded - API key found")
            dpg.configure_item("status_text", color=[100, 255, 100])
        else:
            dpg.set_value("status_text", "Please enter your Gemini API key")
            dpg.configure_item("status_text", color=[255, 200, 100])
    
    def on_file_input_change(self, sender, app_data):
        """Handle manual file path input"""
        file_path = dpg.get_value("file_input").strip()
        if not file_path:
            self.file_path = ""
            return
        
        # Validate file path
        try:
            if os.path.exists(file_path) and (file_path.lower().endswith('.xlsx') or file_path.lower().endswith('.xls')):
                self.file_path = file_path
                display_name = os.path.basename(file_path)
                dpg.set_value("status_text", f"Selected: {display_name}")
                dpg.configure_item("status_text", color=[100, 255, 100])
                
                # Auto-set output folder
                if not self.output_path:
                    output_dir = os.path.join(os.path.dirname(file_path), "output")
                    self.output_path = output_dir
                    dpg.set_value("output_input", output_dir)
            else:
                dpg.set_value("status_text", "File not found or not an Excel file")
                dpg.configure_item("status_text", color=[255, 200, 100])
        except Exception as e:
            dpg.set_value("status_text", f"Error: {str(e)}")
            dpg.configure_item("status_text", color=[255, 100, 100])
    
    def clear_file_input(self):
        """Clear file input"""
        dpg.set_value("file_input", "")
        self.file_path = ""
        dpg.set_value("status_text", "File cleared - select new file")
        dpg.configure_item("status_text", color=[255, 200, 100])
    
    def browse_file(self):
        """Simple file browser - click to select file"""
        try:
            # Create unique dialog tag with timestamp
            import time
            dialog_id = f"file_dialog_{int(time.time())}"
            
            def file_selected(sender, app_data):
                """When user selects a file"""
                try:
                    if 'file_path_name' in app_data:
                        selected_path = app_data['file_path_name']
                        
                        # Check if it's an Excel file
                        if selected_path.lower().endswith(('.xlsx', '.xls')):
                            # Set the path in input field
                            dpg.set_value("file_input", selected_path)
                            self.file_path = selected_path
                            
                            # Update status
                            filename = os.path.basename(selected_path)
                            dpg.set_value("status_text", f"Selected: {filename}")
                            dpg.configure_item("status_text", color=[100, 255, 100])
                            
                            # Auto-set output folder
                            if not self.output_path:
                                output_dir = os.path.join(os.path.dirname(selected_path), "output")
                                self.output_path = output_dir
                                dpg.set_value("output_input", "Auto: output folder next to Excel file")
                        else:
                            self.show_error("Please select an Excel file (.xlsx or .xls)")
                            
                except Exception as e:
                    self.show_error(f"Error: {str(e)}")
                
                # Clean up dialog
                try:
                    dpg.delete_item(dialog_id)
                except:
                    pass
            
            def dialog_cancelled():
                """When user cancels dialog"""
                try:
                    dpg.delete_item(dialog_id)
                except:
                    pass
            
            # Set default directory to user home (contains Desktop, Documents, etc.)
            default_dir = os.path.expanduser("~")

            # Create simple file dialog
            dpg.add_file_dialog(
                label="Select Excel File",
                callback=file_selected,
                cancel_callback=dialog_cancelled,
                tag=dialog_id,
                width=600,
                height=400,
                show=True,
                modal=True,
                directory_selector=False,
                default_path=default_dir
            )
            
            # Add Excel file filters
            try:
                dpg.add_file_extension(".xlsx", parent=dialog_id)
                dpg.add_file_extension(".xls", parent=dialog_id)
            except:
                pass  # Filters are optional
                
        except Exception as e:
            # Fallback if dialog fails
            self.show_error(f"Cannot open file dialog: {str(e)}\n\nYou can manually type or paste the file path in the input field above.")
    
    def browse_output(self):
        """Simple folder browser"""
        try:
            import time
            dialog_id = f"folder_dialog_{int(time.time())}"

            def folder_selected(sender, app_data):
                """When user selects a folder"""
                try:
                    if 'file_path_name' in app_data:
                        selected_path = app_data['file_path_name']

                        # Set the path
                        dpg.set_value("output_input", selected_path)
                        self.output_path = selected_path

                        # Update status
                        folder_name = os.path.basename(selected_path) or selected_path
                        dpg.set_value("status_text", f"Output: {folder_name}")
                        dpg.configure_item("status_text", color=[100, 255, 100])

                except Exception as e:
                    self.show_error(f"Error: {str(e)}")

                # Clean up
                try:
                    dpg.delete_item(dialog_id)
                except:
                    pass

            def dialog_cancelled():
                try:
                    dpg.delete_item(dialog_id)
                except:
                    pass

            # Determine default directory for output folder browser
            if self.file_path and os.path.exists(self.file_path):
                # If input file is selected, open at its directory
                default_dir = os.path.dirname(self.file_path)
            else:
                # Otherwise, open at user home directory
                default_dir = os.path.expanduser("~")

            # Create folder dialog
            dpg.add_file_dialog(
                label="Select Output Folder",
                callback=folder_selected,
                cancel_callback=dialog_cancelled,
                tag=dialog_id,
                width=600,
                height=400,
                show=True,
                modal=True,
                directory_selector=True,
                default_path=default_dir
            )
            
        except Exception as e:
            self.show_error(f"Cannot open folder dialog: {str(e)}\n\nYou can manually type the folder path, or leave empty for auto-generated folder.")
    
    def set_auto_output(self):
        """Set automatic output folder"""
        dpg.set_value("output_input", "")
        self.output_path = ""
        dpg.set_value("status_text", "Will use automatic output folder")
        dpg.configure_item("status_text", color=[100, 255, 100])
    
    def open_downloads_folder(self):
        """Open Downloads folder for reference"""
        try:
            downloads = os.path.expanduser("~/Downloads")
            if sys.platform == "darwin":
                os.system(f"open '{downloads}'")
            elif sys.platform == "win32":
                os.system(f"explorer '{downloads}'")
            else:
                os.system(f"xdg-open '{downloads}'")
        except:
            pass
    
    def open_desktop_folder(self):
        """Open Desktop folder for reference"""
        try:
            desktop = os.path.expanduser("~/Desktop")
            if sys.platform == "darwin":
                os.system(f"open '{desktop}'")
            elif sys.platform == "win32":
                os.system(f"explorer '{desktop}'")
            else:
                os.system(f"xdg-open '{desktop}'")
        except:
            pass
    
    def on_language_change(self, sender, app_data):
        """Handle language selection change"""
        self.target_language = "ja" if app_data == 0 else "vi"
        lang_name = "Japanese" if app_data == 0 else "Vietnamese"
        dpg.set_value("status_text", f"Target language: {lang_name}")
        dpg.configure_item("status_text", color=[100, 255, 100])
    
    def toggle_api_visibility(self):
        """Toggle API key visibility"""
        show_key = dpg.get_value("show_api")
        dpg.configure_item("api_input", password=not show_key)
    
    def open_api_page(self):
        """Open Google AI Studio page"""
        webbrowser.open("https://aistudio.google.com/app/apikey")
        
        # Show info modal
        with dpg.window(label="Get API Key", modal=True, show=True, tag="api_info_modal", 
                       width=400, height=300, pos=[125, 150]):
            dpg.add_text("Steps to get your free API key:")
            dpg.add_separator()
            dpg.add_text("1. Sign in with your Google account")
            dpg.add_text("2. Click 'Create API Key'")
            dpg.add_text("3. Copy the generated key")
            dpg.add_text("4. Paste it in the field above")
            dpg.add_text("5. Click 'Save'")
            dpg.add_separator()
            dpg.add_text("The API is completely free with generous limits!", color=[100, 255, 100])
            dpg.add_spacer(height=20)
            dpg.add_button(label="OK", callback=lambda: dpg.delete_item("api_info_modal"), width=100)
    
    def save_api_key(self):
        """Save API key"""
        api_key = dpg.get_value("api_input").strip()
        if not api_key:
            self.show_error("Please enter an API key first!")
            return
        
        try:
            # Save to .env file
            env_path = os.path.join(SCRIPT_DIR, ".env")
            with open(env_path, "w", encoding="utf-8") as f:
                f.write(f"GEMINI_API_KEY={api_key}\n")
            
            self.api_key = api_key
            self.save_settings()
            
            dpg.set_value("status_text", "API key saved successfully!")
            dpg.configure_item("status_text", color=[100, 255, 100])
            
            self.show_success("API key saved! Ready to translate.")
            
        except Exception as e:
            self.show_error(f"Failed to save API key: {str(e)}")
    
    def show_error(self, message):
        """Show error modal"""
        with dpg.window(label="Error", modal=True, show=True, tag="error_modal",
                       width=400, height=150, pos=[125, 200]):
            dpg.add_text(message, color=[255, 100, 100], wrap=350)
            dpg.add_spacer(height=20)
            dpg.add_button(label="OK", callback=lambda: dpg.delete_item("error_modal"), width=100)
    
    def show_success(self, message):
        """Show success modal"""
        with dpg.window(label="Success", modal=True, show=True, tag="success_modal",
                       width=400, height=150, pos=[125, 200]):
            dpg.add_text(message, color=[100, 255, 100], wrap=350)
            dpg.add_spacer(height=20)
            dpg.add_button(label="OK", callback=lambda: dpg.delete_item("success_modal"), width=100)
    
    def load_settings(self):
        """Load saved settings"""
        try:
            # Load from JSON
            settings_file = os.path.join(SCRIPT_DIR, "translator_settings.json")
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                
                self.api_key = settings.get("api_key", "")
                self.output_path = settings.get("output_folder", "")
                self.target_language = settings.get("language", "ja")
            
            # Load from .env file
            env_path = os.path.join(SCRIPT_DIR, ".env")
            if os.path.exists(env_path):
                with open(env_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.startswith("GEMINI_API_KEY="):
                            key = line.split("=", 1)[1].strip()
                            if key and not self.api_key:
                                self.api_key = key
            
        except Exception as e:
            print(f"Warning: Could not load settings: {e}")
    
    def save_settings(self):
        """Save current settings"""
        try:
            settings = {
                "api_key": dpg.get_value("api_input") if dpg.does_item_exist("api_input") else self.api_key,
                "output_folder": dpg.get_value("output_input") if dpg.does_item_exist("output_input") else self.output_path,
                "language": self.target_language
            }
            
            settings_file = os.path.join(SCRIPT_DIR, "translator_settings.json")
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Warning: Could not save settings: {e}")
    
    def validate_inputs(self):
        """Validate all inputs before translation"""
        if not self.file_path:
            self.show_error("Please select an Excel file!")
            return False
        
        if not os.path.exists(self.file_path):
            self.show_error("Selected file does not exist!")
            return False
        
        api_key = dpg.get_value("api_input").strip()
        if not api_key:
            self.show_error("Please enter your API key and click 'Save'!")
            return False
        
        # Handle output path properly
        output_from_ui = dpg.get_value("output_input").strip()
        
        if not output_from_ui or output_from_ui == "Auto: output folder next to Excel file":
            # Use auto-generated output folder
            default_output = os.path.join(os.path.dirname(self.file_path), "output")
            self.output_path = default_output
            dpg.set_value("output_input", f"Auto: {default_output}")
        else:
            # Use user-specified output folder
            self.output_path = output_from_ui
        
        # Create output directory
        try:
            os.makedirs(self.output_path, exist_ok=True)
            print(f"📁 Output folder: {self.output_path}")  # Debug log
        except Exception as e:
            self.show_error(f"Cannot create output folder: {str(e)}")
            return False
        
        return True
    
    def start_translation(self):
        """Start the translation process"""
        if not self.validate_inputs():
            return
        
        if self.is_translating:
            self.show_error("Translation is already in progress!")
            return
        
        # Update UI
        self.is_translating = True
        dpg.configure_item("start_btn", enabled=False)
        dpg.configure_item("stop_btn", enabled=True)
        dpg.configure_item("progress_bar", show=True)
        dpg.set_value("status_text", "Starting translation...")
        dpg.configure_item("status_text", color=[255, 255, 100])
        
        # Update variables
        self.api_key = dpg.get_value("api_input")
        
        # Save settings
        self.save_settings()
        
        # Start translation in background thread
        self.translation_thread = threading.Thread(target=self.run_translation, daemon=True)
        self.translation_thread.start()
    
    def run_translation(self):
        """Run translation in background thread"""
        try:
            # Import translation module
            from trans_excel2 import process_excel
            
            # Set environment variables BEFORE calling process_excel
            os.environ["GEMINI_API_KEY"] = self.api_key
            os.environ["OPENAI_API_KEY"] = self.api_key  # Fix for OpenAI client library
            os.environ["OUTPUT_DIR"] = self.output_path
            
            print(f"🔧 API Key set: {self.api_key[:20]}...")  # Debug (show first 20 chars)
            print(f"🔧 Output directory set: {self.output_path}")  # Debug
            print(f"🔧 Target language set: {self.target_language}")  # Debug

            # Update status
            dpg.set_value("status_text", "Processing Excel file...")

            # Run translation with proper output path
            result = process_excel(self.file_path, self.target_language, output_dir=self.output_path)
            
            if result and self.is_translating:  # Check if not stopped
                # Translation completed successfully
                dpg.set_value("status_text", "Translation completed successfully!")
                dpg.configure_item("status_text", color=[100, 255, 100])
                self.show_success(f"Translation completed!\n\nOutput saved to:\n{result}")
                
                # Update output field to show actual result location
                actual_output_dir = os.path.dirname(result)
                dpg.set_value("output_input", actual_output_dir)
            
        except ImportError:
            error_msg = "Translation module not found! Please ensure trans_excel2.py is in the same folder."
            dpg.set_value("status_text", "Translation failed")
            dpg.configure_item("status_text", color=[255, 100, 100])
            self.show_error(error_msg)
        except Exception as e:
            error_msg = f"Translation failed: {str(e)}"
            dpg.set_value("status_text", "Translation failed")
            dpg.configure_item("status_text", color=[255, 100, 100])
            self.show_error(error_msg)
            print(f"❌ Translation error: {e}")  # Debug log
        finally:
            # Reset UI
            self.is_translating = False
            dpg.configure_item("start_btn", enabled=True)
            dpg.configure_item("stop_btn", enabled=False)
            dpg.configure_item("progress_bar", show=False)
    
    def stop_translation(self):
        """Stop the translation process"""
        # Show confirmation dialog
        with dpg.window(label="Stop Translation", modal=True, show=True, tag="stop_modal",
                       width=300, height=150, pos=[175, 200]):
            dpg.add_text("Are you sure you want to stop?")
            dpg.add_spacer(height=20)
            with dpg.group(horizontal=True):
                dpg.add_button(label="Yes", callback=self.confirm_stop, width=80)
                dpg.add_button(label="No", callback=lambda: dpg.delete_item("stop_modal"), width=80)
    
    def confirm_stop(self):
        """Confirm stopping translation"""
        self.is_translating = False
        dpg.configure_item("start_btn", enabled=True)
        dpg.configure_item("stop_btn", enabled=False)
        dpg.configure_item("progress_bar", show=False)
        dpg.set_value("status_text", "Translation stopped by user")
        dpg.configure_item("status_text", color=[255, 200, 100])
        dpg.delete_item("stop_modal")
    
    def open_output_folder(self):
        """Open the output folder"""
        output_path = self.output_path or os.path.join(SCRIPT_DIR, "output")
        
        if not os.path.exists(output_path):
            self.show_error("Output folder does not exist yet!")
            return
        
        try:
            if sys.platform == "win32":
                os.startfile(output_path)
            elif sys.platform == "darwin":
                os.system(f"open '{output_path}'")
            else:
                os.system(f"xdg-open '{output_path}'")
        except Exception as e:
            self.show_error(f"Cannot open folder: {str(e)}")
    
    def close_app(self):
        """Close the application"""
        if self.is_translating:
            with dpg.window(label="Quit", modal=True, show=True, tag="quit_modal",
                           width=300, height=150, pos=[175, 200]):
                dpg.add_text("Translation in progress. Quit anyway?")
                dpg.add_spacer(height=20)
                with dpg.group(horizontal=True):
                    dpg.add_button(label="Yes", callback=self.force_quit, width=80)
                    dpg.add_button(label="No", callback=lambda: dpg.delete_item("quit_modal"), width=80)
        else:
            dpg.stop_dearpygui()
    
    def force_quit(self):
        """Force quit the application"""
        self.is_translating = False
        dpg.stop_dearpygui()
    
    def run(self):
        """Start the application"""
        try:
            print("🚀 Starting JaVi Agent Translator...")
            dpg.start_dearpygui()
        except Exception as e:
            print(f"❌ Error running application: {e}")
            import traceback
            traceback.print_exc()
        finally:
            dpg.destroy_context()


def main():
    """Main entry point"""
    try:
        app = ExcelTranslatorApp()
        app.run()
    except Exception as e:
        print(f"❌ Critical error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 