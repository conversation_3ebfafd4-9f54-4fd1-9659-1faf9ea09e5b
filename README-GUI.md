# JaVi Agent Translator - GUI Usage Guide

## Quick Start Guide

### Step 1: Get API Key
1. Click "Get Key" button
2. Sign in to Google AI Studio
3. Create API Key
4. Copy and paste in the app
5. Click "Save"

### Step 2: Select Excel File
1. Click "Browse" to select your Excel file
2. Or paste file path directly

### Step 3: Choose Language
- Japanese (ja)
- Vietnamese (vi)

### Step 4: Start Translation
1. Click "START TRANSLATION"
2. Wait for completion
3. Find translated file in output folder

## Features
- Preserves Excel formatting
- Translates text in cells and shapes
- Batch processing for large files
- Auto-saves settings

## Requirements
- Internet connection for translation
- Valid Gemini API key
- Excel files (.xlsx, .xls)

## Support
For issues, check the main README.md file.
